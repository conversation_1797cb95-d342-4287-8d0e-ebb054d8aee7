import category from "./category"
import generation from "./generation"
import attempt from "./attempt"
import collection from "./collection"
import sharing from "./sharing"

export default [
    {
        path: "quiz",
        name: "Quiz",
        redirect: { name: "Quiz<PERSON><PERSON>" },
        meta: {
            moduleName: "quiz",
            label: "quiz.quiz",
            icon: "fas fa-question-circle",
            defaultChildrenIcon: "fas fa-chevron-right",
            permissions: [
                "quiz:read",
                "quiz-category:read",
                "quiz:attempt",
                "quiz:view-results",
                "quiz:share",
            ],
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "config",
                name: "QuizConfig",
                redirect: { name: "QuizConfigGeneral" },
                meta: {
                    isNotNav: true,
                    type: "config",
                    action: "config",
                    trans: "global.config",
                    label: "config.config",
                    permissions: ["quiz:config"],
                },
                component: () => import("@views/Pages/Quiz/Config/Index.vue"),
                children: [
                    {
                        path: "general",
                        name: "QuizConfigGeneral",
                        meta: {
                            label: "quiz.config.config",
                            icon: "far fa-building",
                            key: "general",
                        },
                        component: () => import("@views/Pages/Quiz/Config/General.vue"),
                    },
                ],
            },
            {
                path: "quizzes",
                name: "QuizLists",
                redirect: { name: "QuizList" },
                meta: {
                    label: "quiz.quizzes",
                    icon: "fas fa-right-long",
                    permissions: ["quiz:read"],
                    hideChildren: true,
                },
                component: {
                    template: "<router-view></router-view>",
                },
                children: [
                    {
                        path: "",
                        name: "QuizList",
                        meta: {
                            trans: "global.list",
                            label: "quiz.quizzes",
                            keySearch: true,
                        },
                        component: () => import("@views/Pages/Quiz/Index.vue"),
                    },
                    {
                        path: "create",
                        name: "QuizCreate",
                        meta: {
                            type: "create",
                            action: "create",
                            trans: "global.add",
                            label: "quiz.quiz",
                            permissions: ["quiz:create"],
                        },
                        component: () => import("@views/Pages/Quiz/Action.vue"),
                    },
                    {
                        path: ":uuid/edit",
                        name: "QuizEdit",
                        meta: {
                            type: "edit",
                            action: "update",
                            trans: "global.edit",
                            label: "quiz.quiz",
                            permissions: ["quiz:edit"],
                        },
                        component: () => import("@views/Pages/Quiz/Action.vue"),
                    },
                    {
                        path: ":uuid",
                        name: "QuizShow",
                        meta: {
                            trans: "global.show",
                            label: "quiz.quiz",
                        },
                        component: () => import("@views/Pages/Quiz/Show.vue"),
                    },
                    {
                        path: ":uuid/preview",
                        name: "QuizPreview",
                        meta: {
                            trans: "quiz.actions.preview",
                            label: "quiz.quiz",
                        },
                        component: () => import("@views/Pages/Quiz/Preview.vue"),
                    },
                    {
                        path: ":uuid/results",
                        name: "QuizResults",
                        meta: {
                            trans: "quiz.actions.view_results",
                            label: "quiz.quiz",
                        },
                        component: () => import("@views/Pages/Quiz/Results.vue"),
                    },
                    {
                        path: ":uuid/analytics",
                        name: "QuizAnalytics",
                        meta: {
                            trans: "quiz.actions.view_analytics",
                            label: "quiz.quiz",
                        },
                        component: () => import("@views/Pages/Quiz/Analytics.vue"),
                    },
                    {
                        path: ":uuid/sharing",
                        name: "QuizSharing",
                        meta: {
                            trans: "quiz.actions.manage_sharing",
                            label: "quiz.quiz",
                            permissions: ["quiz:share"],
                        },
                        component: () => import("@views/Pages/Quiz/Sharing/Index.vue"),
                    },
                ],
            },
            // Include all child route modules using spread operator pattern
            ...category,
            ...generation,
            ...attempt,
            ...collection,
            ...sharing,
        ],
    },
]
